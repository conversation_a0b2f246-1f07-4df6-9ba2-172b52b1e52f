// Advanced lyrics synchronization module
// Handles LRC parsing, timing calculations, and smooth scrolling

class LyricsSync {
    constructor() {
        this.currentTime = 0;
        this.lyrics = [];
        this.activeIndex = -1;
        this.syncOffset = 0; // Milliseconds
        this.isPlaying = false;
        this.smoothScrollEnabled = true;

        this.initializeEventHandlers();
    }

    initializeEventHandlers() {
        // Listen for time updates from YouTube Music
        this.setupTimeObserver();
    }

    setupTimeObserver() {
        // Create a more accurate time observer
        setInterval(() => {
            if (this.isPlaying) {
                this.updateCurrentTime();
            }
        }, 50); // 50ms for smooth updates
    }

    updateCurrentTime() {
        const timeFromPlayer = this.getPlayerCurrentTime();
        if (timeFromPlayer !== null) {
            this.currentTime = timeFromPlayer + (this.syncOffset / 1000);
            this.updateActiveLyric();
        }
    }

    getPlayerCurrentTime() {
        // Multiple methods to get current time from YouTube Music
        try {
            // Method 1: Progress bar approach
            const progressKnob = document.querySelector('#progress-bar #sliderKnob');
            const progressBar = document.querySelector('#progress-bar #sliderBar');

            if (progressKnob && progressBar) {
                const progress = progressKnob.style.left;
                const percentage = parseFloat(progress.replace('%', '')) / 100;

                // Get total duration
                const durationText = document.querySelector('.time-info .ytmusic-player-bar')?.textContent;
                if (durationText) {
                    const timeMatch = durationText.match(/(\d+):(\d+)$/);
                    if (timeMatch) {
                        const totalMinutes = parseInt(timeMatch[1]);
                        const totalSeconds = parseInt(timeMatch[2]);
                        const totalDuration = totalMinutes * 60 + totalSeconds;
                        return totalDuration * percentage;
                    }
                }
            }

            // Method 2: Video element approach (fallback)
            const videoElement = document.querySelector('video');
            if (videoElement && !isNaN(videoElement.currentTime)) {
                return videoElement.currentTime;
            }

            // Method 3: Audio element approach (fallback)
            const audioElement = document.querySelector('audio');
            if (audioElement && !isNaN(audioElement.currentTime)) {
                return audioElement.currentTime;
            }

            return null;
        } catch (error) {
            console.error('Error getting player time:', error);
            return null;
        }
    }

    loadLyrics(lyricsData) {
        if (lyricsData.type === 'synced' && lyricsData.lines) {
            this.lyrics = lyricsData.lines.sort((a, b) => a.time - b.time);
        } else if (lyricsData.type === 'lrc') {
            this.lyrics = this.parseLRC(lyricsData.content);
        } else {
            this.lyrics = [];
        }

        this.activeIndex = -1;
        this.renderLyrics();
    }

    parseLRC(lrcContent) {
        const lines = lrcContent.split('\n');
        const lyricsLines = [];

        for (const line of lines) {
            // Match time tags: [mm:ss.xx] or [mm:ss.xxx]
            const timeMatch = line.match(/\[(\d{1,2}):(\d{2})\.(\d{2,3})\](.*)$/);

            if (timeMatch) {
                const minutes = parseInt(timeMatch[1]);
                const seconds = parseInt(timeMatch[2]);
                let centiseconds = parseInt(timeMatch[3]);

                // Handle both .xx and .xxx formats
                if (timeMatch[3].length === 3) {
                    centiseconds = Math.floor(centiseconds / 10);
                }

                const time = minutes * 60 + seconds + centiseconds / 100;
                const text = timeMatch[4].trim();

                if (text) {
                    lyricsLines.push({ time, text });
                }
            }
        }

        return lyricsLines.sort((a, b) => a.time - b.time);
    }

    renderLyrics() {
        const container = document.querySelector('#lyrics-overlay .lyrics-content');
        if (!container) return;

        if (this.lyrics.length === 0) {
            container.innerHTML = '<div class="lyrics-error">No synchronized lyrics available</div>';
            return;
        }

        container.innerHTML = this.lyrics.map((line, index) => 
            `<div class="lyrics-line" data-time="${line.time}" data-index="${index}">
                ${this.escapeHtml(line.text)}
            </div>`
        ).join('');
    }

    updateActiveLyric() {
        if (this.lyrics.length === 0) return;

        let newActiveIndex = -1;

        // Find the current line based on time
        for (let i = 0; i < this.lyrics.length; i++) {
            if (this.currentTime >= this.lyrics[i].time) {
                newActiveIndex = i;
            } else {
                break;
            }
        }

        // Only update if the active line changed
        if (newActiveIndex !== this.activeIndex) {
            this.setActiveLine(newActiveIndex);
        }
    }

    setActiveLine(index) {
        const lines = document.querySelectorAll('#lyrics-overlay .lyrics-line');

        // Remove previous highlighting
        lines.forEach((line, i) => {
            line.classList.remove('active', 'passed', 'upcoming');

            if (i < index) {
                line.classList.add('passed');
            } else if (i === index) {
                line.classList.add('active');
            } else {
                line.classList.add('upcoming');
            }
        });

        this.activeIndex = index;

        // Auto-scroll to active line
        if (this.smoothScrollEnabled && index >= 0 && lines[index]) {
            this.scrollToLine(lines[index]);
        }

        // Trigger custom event for other components
        document.dispatchEvent(new CustomEvent('lyricsLineChanged', {
            detail: { 
                index, 
                line: this.lyrics[index],
                progress: this.currentTime 
            }
        }));
    }

    scrollToLine(element) {
        const container = document.querySelector('#lyrics-overlay .lyrics-content');
        if (!container || !element) return;

        const containerRect = container.getBoundingClientRect();
        const elementRect = element.getBoundingClientRect();

        // Check if element is outside visible area
        const isOutOfView = elementRect.top < containerRect.top || 
                           elementRect.bottom > containerRect.bottom;

        if (isOutOfView) {
            element.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center',
                inline: 'nearest'
            });
        }
    }

    setPlaybackState(playing) {
        this.isPlaying = playing;
    }

    setSyncOffset(offsetMs) {
        this.syncOffset = offsetMs;
    }

    setSmoothScroll(enabled) {
        this.smoothScrollEnabled = enabled;
    }

    jumpToTime(time) {
        this.currentTime = time;
        this.updateActiveLyric();
    }

    getProgress() {
        if (this.lyrics.length === 0) return 0;

        const totalDuration = this.lyrics[this.lyrics.length - 1].time;
        return Math.min(this.currentTime / totalDuration, 1);
    }

    getCurrentLine() {
        if (this.activeIndex >= 0 && this.activeIndex < this.lyrics.length) {
            return this.lyrics[this.activeIndex];
        }
        return null;
    }

    getUpcomingLines(count = 3) {
        const upcoming = [];
        for (let i = this.activeIndex + 1; i < Math.min(this.activeIndex + 1 + count, this.lyrics.length); i++) {
            upcoming.push(this.lyrics[i]);
        }
        return upcoming;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Advanced synchronization features
    calibrateSync() {
        // Auto-calibration based on audio analysis (future enhancement)
        console.log('Auto-calibration not yet implemented');
    }

    exportCurrentSession() {
        return {
            lyrics: this.lyrics,
            syncOffset: this.syncOffset,
            timestamp: Date.now()
        };
    }

    importSession(sessionData) {
        if (sessionData.lyrics) {
            this.lyrics = sessionData.lyrics;
            this.syncOffset = sessionData.syncOffset || 0;
            this.renderLyrics();
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LyricsSync;
} else {
    window.LyricsSync = LyricsSync;
}
