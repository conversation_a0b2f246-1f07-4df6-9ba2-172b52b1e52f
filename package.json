{"name": "youtube-music-live-lyrics", "version": "1.0.0", "description": "Live synchronized lyrics overlay for YouTube Music", "main": "content.js", "scripts": {"build": "echo 'Building extension package...'", "test": "echo 'No tests specified'", "lint": "echo 'Linting JavaScript files...'"}, "keywords": ["youtube-music", "lyrics", "browser-extension", "chrome", "edge", "music"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/youtube-music-lyrics-extension.git"}, "bugs": {"url": "https://github.com/your-username/youtube-music-lyrics-extension/issues"}, "homepage": "https://github.com/your-username/youtube-music-lyrics-extension#readme"}