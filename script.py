# Create the browser extension files
import json
import os

# Create directory structure
os.makedirs('youtube-music-lyrics-extension', exist_ok=True)
os.chdir('youtube-music-lyrics-extension')

# Create manifest.json for Chrome/Edge extension
manifest = {
    "manifest_version": 3,
    "name": "YouTube Music Live Lyrics",
    "version": "1.0.0",
    "description": "Auto-scrolling synchronized lyrics overlay for YouTube Music",
    "permissions": [
        "activeTab",
        "storage",
        "scripting"
    ],
    "host_permissions": [
        "https://music.youtube.com/*",
        "https://api.genius.com/*",
        "https://www.musixmatch.com/*"
    ],
    "content_scripts": [
        {
            "matches": ["https://music.youtube.com/*"],
            "js": ["content.js"],
            "css": ["styles.css"],
            "run_at": "document_end"
        }
    ],
    "background": {
        "service_worker": "background.js"
    },
    "action": {
        "default_popup": "popup.html",
        "default_title": "YouTube Music Lyrics"
    },
    "web_accessible_resources": [
        {
            "resources": ["lyrics-overlay.html", "lyrics-sync.js"],
            "matches": ["https://music.youtube.com/*"]
        }
    ]
}

with open('manifest.json', 'w') as f:
    json.dump(manifest, f, indent=2)

print("✅ Created manifest.json")
print("📁 Extension directory structure:")
print("youtube-music-lyrics-extension/")
print("├── manifest.json")
print("├── content.js (to be created)")
print("├── background.js (to be created)")  
print("├── popup.html (to be created)")
print("├── popup.js (to be created)")
print("├── styles.css (to be created)")
print("├── lyrics-overlay.html (to be created)")
print("└── lyrics-sync.js (to be created)")