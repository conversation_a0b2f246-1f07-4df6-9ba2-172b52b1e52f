<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Music Lyrics</title>
    <style>
        body {
            width: 320px;
            min-height: 400px;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .header {
            background: linear-gradient(90deg, #ff6b35 0%, #f7931e 100%);
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 18px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .header p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 12px;
        }

        .content {
            padding: 20px;
        }

        .current-song {
            background: rgba(255, 107, 53, 0.1);
            border: 1px solid rgba(255, 107, 53, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .current-song h3 {
            margin: 0 0 10px 0;
            color: #ff6b35;
            font-size: 14px;
        }

        .song-info {
            font-size: 13px;
            line-height: 1.4;
        }

        .song-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .song-artist {
            opacity: 0.8;
        }

        .controls {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .control-group {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .control-label {
            font-size: 13px;
            flex: 1;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #444;
            transition: 0.3s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: 0.3s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #ff6b35;
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        .btn {
            background: linear-gradient(90deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
            transition: all 0.2s ease;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            text-align: center;
        }

        .status.success {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
            color: #00ff00;
        }

        .status.error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff4444;
        }

        .status.info {
            background: rgba(0, 123, 255, 0.1);
            border: 1px solid rgba(0, 123, 255, 0.3);
            color: #007bff;
        }

        .footer {
            padding: 15px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            font-size: 11px;
            opacity: 0.7;
        }

        .footer a {
            color: #ff6b35;
            text-decoration: none;
        }

        .loading {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 107, 53, 0.3);
            border-radius: 50%;
            border-top-color: #ff6b35;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎵 YouTube Music Lyrics</h1>
        <p>Live synchronized lyrics overlay</p>
    </div>

    <div class="content">
        <div class="current-song">
            <h3>Current Song</h3>
            <div class="song-info" id="song-info">
                <div class="song-title" id="song-title">No song detected</div>
                <div class="song-artist" id="song-artist">Open YouTube Music to start</div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <span class="control-label">Enable Lyrics</span>
                <label class="switch">
                    <input type="checkbox" id="toggle-lyrics" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="control-group">
                <span class="control-label">Auto-scroll</span>
                <label class="switch">
                    <input type="checkbox" id="toggle-autoscroll" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <div class="control-group">
                <span class="control-label">Show overlay</span>
                <label class="switch">
                    <input type="checkbox" id="toggle-overlay" checked>
                    <span class="slider"></span>
                </label>
            </div>

            <button class="btn" id="refresh-lyrics">
                <span id="refresh-text">🔄 Refresh Lyrics</span>
                <span class="loading hidden" id="refresh-loading"></span>
            </button>
        </div>

        <div class="status hidden" id="status"></div>
    </div>

    <div class="footer">
        Made with ❤️ for music lovers<br>
        <a href="#" id="github-link">View on GitHub</a>
    </div>

    <script src="popup.js"></script>
</body>
</html>