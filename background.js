// Background script for YouTube Music Live Lyrics Extension
// Handles API calls and lyrics fetching

class LyricsAPI {
    constructor() {
        this.setupMessageListener();
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'fetchLyrics') {
                this.fetchLyrics(request.song).then((result) => {
                    console.log('Sending lyrics response:', result);
                    sendResponse(result);
                }).catch((error) => {
                    console.error('Error in fetchLyrics:', error);
                    sendResponse({ error: 'Failed to fetch lyrics' });
                });
                return true; // Keep the messaging channel open for async response
            }
        });
    }

    async fetchLyrics(songInfo) {
        console.log('🔍 Fetching lyrics for:', songInfo);

        try {
            // Try real lyrics APIs first
            console.log('🔍 Fetching real lyrics for:', songInfo.title, 'by', songInfo.artist);
            let lyrics = await this.tryLyricsOVH(songInfo);

            if (!lyrics) {
                console.log('lyrics.ovh failed, trying alternative APIs...');
                lyrics = await this.tryLyricsAPI(songInfo);
            }

            if (!lyrics) {
                console.log('All APIs failed, trying web scraping...');
                lyrics = await this.tryWebScraping(songInfo);
            }

            // Use demo lyrics as final fallback
            if (!lyrics) {
                console.log('🎵 Using demo synchronized lyrics as fallback');
                lyrics = await this.tryMusixmatchAPI(songInfo);
            }

            if (lyrics) {
                return { lyrics: lyrics };
            } else {
                return { error: 'No lyrics found' };
            }

        } catch (error) {
            console.error('Error fetching lyrics:', error);
            return { error: 'Failed to fetch lyrics' };
        }
    }

    async tryLyricsOVH(songInfo) {
        try {
            console.log('🎵 Trying lyrics.ovh API for:', songInfo.artist, '-', songInfo.title);

            // Clean up song title (remove extra info in brackets)
            const cleanTitle = songInfo.title.replace(/\[.*?\]/g, '').replace(/\(.*?\)/g, '').trim();
            const cleanArtist = songInfo.artist.split(',')[0].trim(); // Take first artist if multiple

            console.log('🧹 Cleaned search:', cleanArtist, '-', cleanTitle);

            const response = await fetch(`https://api.lyrics.ovh/v1/${encodeURIComponent(cleanArtist)}/${encodeURIComponent(cleanTitle)}`);

            console.log('📡 API response status:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('📄 API response received, lyrics length:', data.lyrics ? data.lyrics.length : 0);

                if (data.lyrics && data.lyrics.trim()) {
                    return {
                        type: 'static',
                        text: this.formatLyrics(data.lyrics),
                        source: 'lyrics.ovh'
                    };
                }
            } else {
                console.log('❌ API response not ok:', response.status, response.statusText);
            }

            return null;
        } catch (error) {
            console.error('❌ Lyrics.ovh API error:', error);
            return null;
        }
    }

    async tryLyricsAPI(songInfo) {
        try {
            console.log('🎵 Trying alternative lyrics API for:', songInfo.artist, '-', songInfo.title);

            // Try a different approach - using a CORS proxy or alternative API
            // For now, let's try a simple approach with cleaned search terms
            const cleanTitle = songInfo.title.replace(/\[.*?\]/g, '').replace(/\(.*?\)/g, '').trim();
            const cleanArtist = songInfo.artist.split(',')[0].trim();

            // Alternative: Try with different API endpoint or proxy
            // Note: In production, you might want to use a CORS proxy service
            const proxyUrl = 'https://cors-anywhere.herokuapp.com/';
            const apiUrl = `https://api.lyrics.ovh/v1/${encodeURIComponent(cleanArtist)}/${encodeURIComponent(cleanTitle)}`;

            console.log('🔄 Trying with CORS proxy...');
            const response = await fetch(proxyUrl + apiUrl);

            if (response.ok) {
                const data = await response.json();
                if (data.lyrics && data.lyrics.trim()) {
                    return {
                        type: 'static',
                        text: this.formatLyrics(data.lyrics),
                        source: 'lyrics.ovh-proxy'
                    };
                }
            }

            return null;
        } catch (error) {
            console.error('❌ Alternative lyrics API error:', error);
            return null;
        }
    }

    async tryMusixmatchAPI(songInfo) {
        try {
            // Note: This would require Musixmatch API integration
            // For demo purposes, we'll simulate synchronized lyrics
            return this.generateDemoSyncedLyrics(songInfo);
        } catch (error) {
            console.error('Musixmatch API error:', error);
            return null;
        }
    }

    async tryWebScraping(songInfo) {
        try {
            console.log('🕷️ Trying additional lyrics sources for:', songInfo.artist, '-', songInfo.title);

            const cleanTitle = songInfo.title.replace(/\[.*?\]/g, '').replace(/\(.*?\)/g, '').trim();
            const cleanArtist = songInfo.artist.split(',')[0].trim();

            // Try alternative free lyrics APIs
            const apis = [
                `https://lyrist.vercel.app/api/${encodeURIComponent(cleanArtist)}/${encodeURIComponent(cleanTitle)}`,
                `https://some-random-api.ml/lyrics?title=${encodeURIComponent(cleanTitle)}&artist=${encodeURIComponent(cleanArtist)}`
            ];

            for (const apiUrl of apis) {
                try {
                    console.log('🔍 Trying API:', apiUrl);
                    const response = await fetch(apiUrl);

                    if (response.ok) {
                        const data = await response.json();
                        console.log('📄 Response received from alternative API');

                        // Handle different response formats
                        let lyricsText = null;
                        if (data.lyrics) {
                            lyricsText = data.lyrics;
                        } else if (data.data && data.data.lyrics) {
                            lyricsText = data.data.lyrics;
                        } else if (typeof data === 'string') {
                            lyricsText = data;
                        }

                        if (lyricsText && lyricsText.trim()) {
                            return {
                                type: 'static',
                                text: this.formatLyrics(lyricsText),
                                source: 'alternative-api'
                            };
                        }
                    }
                } catch (apiError) {
                    console.log('❌ API failed:', apiError.message);
                    continue;
                }
            }

            return null;
        } catch (error) {
            console.error('❌ Web scraping error:', error);
            return null;
        }
    }

    formatLyrics(rawLyrics) {
        if (!rawLyrics) return '';

        return rawLyrics
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0 && !line.match(/^\[.*\]$/)) // Remove timestamp markers
            .filter(line => !line.toLowerCase().includes('paroles de') && !line.toLowerCase().includes('lyrics by')) // Remove attribution lines
            .join('\n');
    }

    // Generate demo synchronized lyrics for testing
    generateDemoSyncedLyrics(songInfo) {
        const demoLines = [
            { time: 0, text: `♪ ${songInfo.title} ♪` },
            { time: 5, text: `by ${songInfo.artist}` },
            { time: 10, text: "Demo synchronized lyrics" },
            { time: 15, text: "This is line 1 of the song" },
            { time: 20, text: "Here comes the second line" },
            { time: 25, text: "Third line with perfect timing" },
            { time: 30, text: "Chorus begins here now" },
            { time: 35, text: "Singing along with the beat" },
            { time: 40, text: "Every word is synchronized" },
            { time: 45, text: "With the music playing" },
            { time: 50, text: "Bridge section starts" },
            { time: 55, text: "Building up to the climax" },
            { time: 60, text: "Final chorus incoming" },
            { time: 65, text: "Last line of the song" },
            { time: 70, text: "♪ ♪ ♪" }
        ];

        return {
            type: 'synced',
            lines: demoLines,
            source: 'demo'
        };
    }

    // Parse LRC file format
    parseLRC(lrcContent) {
        const lines = lrcContent.split('\n');
        const lyricsLines = [];
        const metadata = {};

        for (const line of lines) {
            const timeMatch = line.match(/\[(\d{2}):(\d{2})\.(\d{2})\](.*)$/);
            const metaMatch = line.match(/\[([a-z]+):(.*)\]/);

            if (timeMatch) {
                const minutes = parseInt(timeMatch[1]);
                const seconds = parseInt(timeMatch[2]);
                const centiseconds = parseInt(timeMatch[3]);
                const time = minutes * 60 + seconds + centiseconds / 100;
                const text = timeMatch[4].trim();

                if (text) {
                    lyricsLines.push({ time, text });
                }
            } else if (metaMatch) {
                metadata[metaMatch[1]] = metaMatch[2];
            }
        }

        return {
            type: 'synced',
            lines: lyricsLines,
            metadata,
            source: 'lrc'
        };
    }
}

// Initialize the background script
new LyricsAPI();

// Handle extension installation
chrome.runtime.onInstalled.addListener(() => {
    console.log('🎵 YouTube Music Lyrics Extension installed');
});
