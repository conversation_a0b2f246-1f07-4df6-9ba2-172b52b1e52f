// Background script for YouTube Music Live Lyrics Extension
// Handles API calls and lyrics fetching

class LyricsAPI {
    constructor() {
        this.setupMessageListener();
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'fetchLyrics') {
                this.fetchLyrics(request.song).then((result) => {
                    console.log('Sending lyrics response:', result);
                    sendResponse(result);
                }).catch((error) => {
                    console.error('Error in fetchLyrics:', error);
                    sendResponse({ error: 'Failed to fetch lyrics' });
                });
                return true; // Keep the messaging channel open for async response
            }
        });
    }

    async fetchLyrics(songInfo) {
        console.log('🔍 Fetching lyrics for:', songInfo);

        try {
            // Try multiple sources in order of preference
            let lyrics = await this.tryGeniusAPI(songInfo);
            if (!lyrics) {
                console.log('Lyrics API failed, trying demo lyrics...');
                lyrics = await this.tryMusixmatchAPI(songInfo);
            }
            if (!lyrics) {
                lyrics = await this.tryWebScraping(songInfo);
            }

            if (lyrics) {
                return { lyrics: lyrics };
            } else {
                return { error: 'No lyrics found' };
            }

        } catch (error) {
            console.error('Error fetching lyrics:', error);
            return { error: 'Failed to fetch lyrics' };
        }
    }

    async tryGeniusAPI(songInfo) {
        try {
            console.log('Trying lyrics.ovh API for:', songInfo);

            // Using a public lyrics API as fallback
            const response = await fetch(`https://api.lyrics.ovh/v1/${encodeURIComponent(songInfo.artist)}/${encodeURIComponent(songInfo.title)}`);

            console.log('API response status:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('API response data:', data);

                if (data.lyrics) {
                    return {
                        type: 'static',
                        text: this.formatLyrics(data.lyrics),
                        source: 'lyrics.ovh'
                    };
                }
            } else {
                console.log('API response not ok:', response.status, response.statusText);
            }

            return null;
        } catch (error) {
            console.error('Lyrics API error:', error);
            return null;
        }
    }

    async tryMusixmatchAPI(songInfo) {
        try {
            // Note: This would require Musixmatch API integration
            // For demo purposes, we'll simulate synchronized lyrics
            return this.generateDemoSyncedLyrics(songInfo);
        } catch (error) {
            console.error('Musixmatch API error:', error);
            return null;
        }
    }

    async tryWebScraping(songInfo) {
        try {
            // Note: Web scraping should be done carefully and in compliance with ToS
            // This is a simplified example for demonstration
            console.log('Web scraping not implemented in demo');
            return null;
        } catch (error) {
            console.error('Web scraping error:', error);
            return null;
        }
    }

    formatLyrics(rawLyrics) {
        return rawLyrics
            .split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0)
            .join('\n');
    }

    // Generate demo synchronized lyrics for testing
    generateDemoSyncedLyrics(songInfo) {
        const demoLines = [
            { time: 0, text: `♪ ${songInfo.title} ♪` },
            { time: 5, text: `by ${songInfo.artist}` },
            { time: 10, text: "Demo synchronized lyrics" },
            { time: 15, text: "This is line 1 of the song" },
            { time: 20, text: "Here comes the second line" },
            { time: 25, text: "Third line with perfect timing" },
            { time: 30, text: "Chorus begins here now" },
            { time: 35, text: "Singing along with the beat" },
            { time: 40, text: "Every word is synchronized" },
            { time: 45, text: "With the music playing" },
            { time: 50, text: "Bridge section starts" },
            { time: 55, text: "Building up to the climax" },
            { time: 60, text: "Final chorus incoming" },
            { time: 65, text: "Last line of the song" },
            { time: 70, text: "♪ ♪ ♪" }
        ];

        return {
            type: 'synced',
            lines: demoLines,
            source: 'demo'
        };
    }

    // Parse LRC file format
    parseLRC(lrcContent) {
        const lines = lrcContent.split('\n');
        const lyricsLines = [];
        const metadata = {};

        for (const line of lines) {
            const timeMatch = line.match(/\[(\d{2}):(\d{2})\.(\d{2})\](.*)$/);
            const metaMatch = line.match(/\[([a-z]+):(.*)\]/);

            if (timeMatch) {
                const minutes = parseInt(timeMatch[1]);
                const seconds = parseInt(timeMatch[2]);
                const centiseconds = parseInt(timeMatch[3]);
                const time = minutes * 60 + seconds + centiseconds / 100;
                const text = timeMatch[4].trim();

                if (text) {
                    lyricsLines.push({ time, text });
                }
            } else if (metaMatch) {
                metadata[metaMatch[1]] = metaMatch[2];
            }
        }

        return {
            type: 'synced',
            lines: lyricsLines,
            metadata,
            source: 'lrc'
        };
    }
}

// Initialize the background script
new LyricsAPI();

// Handle extension installation
chrome.runtime.onInstalled.addListener(() => {
    console.log('🎵 YouTube Music Lyrics Extension installed');
});
