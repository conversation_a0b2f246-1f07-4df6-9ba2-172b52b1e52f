// Popup script for YouTube Music Live Lyrics Extension

class PopupController {
    constructor() {
        this.currentSong = null;
        this.settings = {
            lyricsEnabled: true,
            autoScroll: true,
            showOverlay: true
        };

        this.init();
    }

    init() {
        this.loadSettings();
        this.setupEventListeners();
        this.checkCurrentSong();
        this.updateStatus('Ready to fetch lyrics', 'info');
    }

    setupEventListeners() {
        // Toggle switches
        document.getElementById('toggle-lyrics').addEventListener('change', (e) => {
            this.settings.lyricsEnabled = e.target.checked;
            this.saveSettings();
            this.sendMessageToContent('toggleLyrics', { enabled: e.target.checked });
        });

        document.getElementById('toggle-autoscroll').addEventListener('change', (e) => {
            this.settings.autoScroll = e.target.checked;
            this.saveSettings();
            this.sendMessageToContent('toggleAutoScroll', { enabled: e.target.checked });
        });

        document.getElementById('toggle-overlay').addEventListener('change', (e) => {
            this.settings.showOverlay = e.target.checked;
            this.saveSettings();
            this.sendMessageToContent('toggleOverlay', { enabled: e.target.checked });
        });

        // Refresh button
        document.getElementById('refresh-lyrics').addEventListener('click', () => {
            this.refreshLyrics();
        });

        // GitHub link
        document.getElementById('github-link').addEventListener('click', () => {
            chrome.tabs.create({ url: 'https://github.com/your-username/youtube-music-lyrics-extension' });
        });
    }

    async checkCurrentSong() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (tab.url && tab.url.includes('music.youtube.com')) {
                // Send message to content script to get current song
                chrome.tabs.sendMessage(tab.id, { action: 'getCurrentSong' }, (response) => {
                    if (response) {
                        this.updateCurrentSong(response);
                    } else {
                        this.updateCurrentSong(null);
                    }
                });
            } else {
                this.updateStatus('Please open YouTube Music', 'info');
                this.updateCurrentSong(null);
            }
        } catch (error) {
            console.error('Error checking current song:', error);
            this.updateStatus('Error: Cannot connect to YouTube Music', 'error');
        }
    }

    updateCurrentSong(songInfo) {
        const titleElement = document.getElementById('song-title');
        const artistElement = document.getElementById('song-artist');

        if (songInfo) {
            titleElement.textContent = songInfo.title;
            artistElement.textContent = songInfo.artist;
            this.currentSong = songInfo;
            this.updateStatus('Song detected successfully', 'success');
        } else {
            titleElement.textContent = 'No song detected';
            artistElement.textContent = 'Open YouTube Music to start';
            this.currentSong = null;
        }
    }

    async refreshLyrics() {
        if (!this.currentSong) {
            this.updateStatus('No song to refresh lyrics for', 'error');
            return;
        }

        this.showLoading(true);
        this.updateStatus('Fetching fresh lyrics...', 'info');

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            chrome.tabs.sendMessage(tab.id, { 
                action: 'refreshLyrics', 
                song: this.currentSong 
            }, (response) => {
                this.showLoading(false);
                if (response && response.success) {
                    this.updateStatus('Lyrics refreshed successfully', 'success');
                } else {
                    this.updateStatus('Failed to refresh lyrics', 'error');
                }
            });
        } catch (error) {
            this.showLoading(false);
            this.updateStatus('Error refreshing lyrics', 'error');
            console.error('Refresh error:', error);
        }
    }

    showLoading(show) {
        const refreshText = document.getElementById('refresh-text');
        const refreshLoading = document.getElementById('refresh-loading');
        const refreshButton = document.getElementById('refresh-lyrics');

        if (show) {
            refreshText.classList.add('hidden');
            refreshLoading.classList.remove('hidden');
            refreshButton.disabled = true;
        } else {
            refreshText.classList.remove('hidden');
            refreshLoading.classList.add('hidden');
            refreshButton.disabled = false;
        }
    }

    updateStatus(message, type = 'info') {
        const statusElement = document.getElementById('status');
        statusElement.textContent = message;
        statusElement.className = `status ${type}`;
        statusElement.classList.remove('hidden');

        // Auto-hide after 3 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                statusElement.classList.add('hidden');
            }, 3000);
        }
    }

    async sendMessageToContent(action, data = {}) {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tab.url && tab.url.includes('music.youtube.com')) {
                chrome.tabs.sendMessage(tab.id, { action, ...data });
            }
        } catch (error) {
            console.error('Error sending message to content script:', error);
        }
    }

    loadSettings() {
        chrome.storage.sync.get(['lyricsSettings'], (result) => {
            if (result.lyricsSettings) {
                this.settings = { ...this.settings, ...result.lyricsSettings };
                this.updateUI();
            }
        });
    }

    saveSettings() {
        chrome.storage.sync.set({ lyricsSettings: this.settings });
    }

    updateUI() {
        document.getElementById('toggle-lyrics').checked = this.settings.lyricsEnabled;
        document.getElementById('toggle-autoscroll').checked = this.settings.autoScroll;
        document.getElementById('toggle-overlay').checked = this.settings.showOverlay;
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PopupController();
});
