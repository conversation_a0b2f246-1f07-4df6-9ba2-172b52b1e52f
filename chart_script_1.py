import plotly.graph_objects as go
import plotly.express as px
import json

# Parse the data
data = {
    "steps": [
        {"number": 1, "title": "Download Extension", "description": "Get the extension files from GitHub or build folder", "icon": "📥"},
        {"number": 2, "title": "Open Extensions Page", "description": "Navigate to chrome://extensions/ or edge://extensions/", "icon": "🌐"},
        {"number": 3, "title": "Enable Developer Mode", "description": "Toggle the Developer mode switch", "icon": "🔧"},
        {"number": 4, "title": "Load Unpacked", "description": "Click the 'Load unpacked' button", "icon": "📁"},
        {"number": 5, "title": "Select Folder", "description": "Choose the extension directory", "icon": "📂"},
        {"number": 6, "title": "Grant Permissions", "description": "Allow access to YouTube Music", "icon": "✅"},
        {"number": 7, "title": "Open YouTube Music", "description": "Navigate to music.youtube.com", "icon": "🎵"},
        {"number": 8, "title": "Enjoy Lyrics", "description": "Play songs and see synchronized lyrics", "icon": "🎤"}
    ]
}

# Extract step information
steps = data["steps"]
x_coords = [step["number"] for step in steps]
y_coords = [1] * len(steps)  # All on same horizontal line

# Abbreviate titles to fit 15-character limit
abbreviated_titles = [
    "Download Ext",
    "Open Ext Page", 
    "Enable Dev Mode",
    "Load Unpacked",
    "Select Folder",
    "Grant Perms",
    "Open YT Music", 
    "Enjoy Lyrics"
]

# Create text labels with icons and abbreviated titles
text_labels = [f"{step['icon']}<br>{abbreviated_titles[i]}" for i, step in enumerate(steps)]

# Create hover text with full descriptions
hover_text = [f"Step {step['number']}: {step['title']}<br>{step['description']}" for step in steps]

# Brand colors
colors = ['#1FB8CD', '#FFC185', '#ECEBD5', '#5D878F', '#D2BA4C', '#B4413C', '#964325', '#944454']

fig = go.Figure()

# Add connecting line
fig.add_trace(go.Scatter(
    x=x_coords,
    y=y_coords,
    mode='lines',
    line=dict(color='#5D878F', width=3),
    showlegend=False,
    hoverinfo='skip',
    cliponaxis=False
))

# Add step points
fig.add_trace(go.Scatter(
    x=x_coords,
    y=y_coords,
    mode='markers+text',
    marker=dict(
        size=40,
        color=[colors[i % len(colors)] for i in range(len(steps))],
        line=dict(width=2, color='white')
    ),
    text=text_labels,
    textposition='bottom center',
    textfont=dict(size=12),
    hovertext=hover_text,
    hoverinfo='text',
    showlegend=False,
    cliponaxis=False
))

# Update layout
fig.update_layout(
    title="YT Music Lyrics Extension Install",
    xaxis=dict(
        title="Install Steps",
        tickmode='linear',
        tick0=1,
        dtick=1,
        range=[0.5, 8.5]
    ),
    yaxis=dict(
        showticklabels=False,
        showgrid=False,
        zeroline=False,
        range=[0.5, 1.5]
    ),
    showlegend=False,
    plot_bgcolor='rgba(0,0,0,0)'
)

# Save the chart
fig.write_image("installation_flowchart.png")