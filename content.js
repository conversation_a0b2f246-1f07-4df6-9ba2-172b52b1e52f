// Content script for YouTube Music Live Lyrics Extension
// This script runs on music.youtube.com and detects the current song

class YouTubeMusicLyricsExtension {
    constructor() {
        this.currentSong = null;
        this.lyricsOverlay = null;
        this.isEnabled = true;
        this.syncInterval = null;
        this.lyricsData = null;

        this.init();
    }

    init() {
        console.log('🎵 YouTube Music Lyrics Extension initialized');
        this.createLyricsOverlay();
        this.observeSongChanges();
        this.setupMessageListener();
    }

    // Detect current song information
    getCurrentSongInfo() {
        const titleElement = document.querySelector('.content-info-wrapper .title');
        const artistElement = document.querySelector('.content-info-wrapper .subtitle a');

        if (titleElement && artistElement) {
            const title = titleElement.textContent.trim();
            const artist = artistElement.textContent.trim();
            return { title, artist };
        }
        return null;
    }

    // Create floating lyrics overlay
    createLyricsOverlay() {
        if (this.lyricsOverlay) return;

        this.lyricsOverlay = document.createElement('div');
        this.lyricsOverlay.id = 'lyrics-overlay';
        this.lyricsOverlay.innerHTML = `
            <div class="lyrics-header">
                <span class="lyrics-title">🎵 Live Lyrics</span>
                <div class="lyrics-controls">
                    <button id="lyrics-toggle">⏸️</button>
                    <button id="lyrics-close">✖️</button>
                </div>
            </div>
            <div class="lyrics-content">
                <div class="lyrics-loading">🎤 Searching for lyrics...</div>
            </div>
        `;

        document.body.appendChild(this.lyricsOverlay);
        this.setupOverlayControls();
    }

    setupOverlayControls() {
        const toggleBtn = document.getElementById('lyrics-toggle');
        const closeBtn = document.getElementById('lyrics-close');

        toggleBtn?.addEventListener('click', () => this.toggleLyrics());
        closeBtn?.addEventListener('click', () => this.hideLyricsOverlay());

        // Make overlay draggable
        this.makeDraggable(this.lyricsOverlay);
    }

    makeDraggable(element) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        const header = element.querySelector('.lyrics-header');

        header.onmousedown = dragMouseDown;

        function dragMouseDown(e) {
            e = e || window.event;
            e.preventDefault();
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;
        }

        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            element.style.top = (element.offsetTop - pos2) + "px";
            element.style.left = (element.offsetLeft - pos1) + "px";
        }

        function closeDragElement() {
            document.onmouseup = null;
            document.onmousemove = null;
        }
    }

    // Observe for song changes
    observeSongChanges() {
        const observer = new MutationObserver(() => {
            const songInfo = this.getCurrentSongInfo();
            if (songInfo && JSON.stringify(songInfo) !== JSON.stringify(this.currentSong)) {
                this.currentSong = songInfo;
                this.fetchLyrics(songInfo);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Fetch lyrics from API
    async fetchLyrics(songInfo) {
        try {
            this.showLoading();

            // Send message to background script to fetch lyrics
            chrome.runtime.sendMessage({
                action: 'fetchLyrics',
                song: songInfo
            }, (response) => {
                console.log('Received lyrics response:', response);

                if (chrome.runtime.lastError) {
                    console.error('Chrome runtime error:', chrome.runtime.lastError);
                    this.showError('Extension communication error');
                    return;
                }

                if (response && response.lyrics) {
                    this.displayLyrics(response.lyrics);
                    this.startSynchronization();
                } else if (response && response.error) {
                    this.showError(response.error);
                } else {
                    this.showError('Lyrics not found');
                }
            });

        } catch (error) {
            console.error('Error fetching lyrics:', error);
            this.showError('Failed to fetch lyrics');
        }
    }

    // Display lyrics in overlay
    displayLyrics(lyricsData) {
        this.lyricsData = lyricsData;
        const content = this.lyricsOverlay.querySelector('.lyrics-content');

        if (lyricsData.type === 'synced') {
            // Display synchronized lyrics
            content.innerHTML = lyricsData.lines.map((line, index) =>
                `<div class="lyrics-line" data-time="${line.time}" data-index="${index}">
                    ${line.text}
                </div>`
            ).join('');
        } else {
            // Display static lyrics
            content.innerHTML = `<div class="static-lyrics">${lyricsData.text}</div>`;
        }

        this.lyricsOverlay.style.display = 'block';
    }

    // Start lyrics synchronization
    startSynchronization() {
        if (!this.lyricsData || this.lyricsData.type !== 'synced') return;

        this.stopSynchronization();

        this.syncInterval = setInterval(() => {
            const currentTime = this.getCurrentPlaybackTime();
            if (currentTime !== null) {
                this.highlightCurrentLine(currentTime);
            }
        }, 100); // Update every 100ms for smooth sync
    }

    stopSynchronization() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
    }

    // Get current playback time from YouTube Music player
    getCurrentPlaybackTime() {
        const timeElement = document.querySelector('#progress-bar #sliderKnob');
        const progressBar = document.querySelector('#progress-bar #sliderBar');

        if (timeElement && progressBar) {
            const progress = timeElement.style.left;
            const percentage = parseFloat(progress) / 100;

            // Get duration from time display
            const timeDisplay = document.querySelector('.time-info');
            if (timeDisplay) {
                const timeText = timeDisplay.textContent;
                const match = timeText.match(/(\d+):(\d+)/);
                if (match) {
                    const totalSeconds = parseInt(match[1]) * 60 + parseInt(match[2]);
                    return totalSeconds * percentage;
                }
            }
        }

        return null;
    }

    // Highlight current lyrics line and auto-scroll
    highlightCurrentLine(currentTime) {
        const lines = this.lyricsOverlay.querySelectorAll('.lyrics-line');
        let activeIndex = -1;

        // Find the current line based on time
        for (let i = 0; i < lines.length; i++) {
            const lineTime = parseFloat(lines[i].dataset.time);
            if (currentTime >= lineTime) {
                activeIndex = i;
            } else {
                break;
            }
        }

        // Remove previous highlighting
        lines.forEach(line => line.classList.remove('active', 'passed'));

        // Highlight current and passed lines
        if (activeIndex >= 0) {
            for (let i = 0; i <= activeIndex; i++) {
                if (i === activeIndex) {
                    lines[i].classList.add('active');
                } else {
                    lines[i].classList.add('passed');
                }
            }

            // Auto-scroll to current line
            this.scrollToLine(lines[activeIndex]);
        }
    }

    scrollToLine(element) {
        const container = this.lyricsOverlay.querySelector('.lyrics-content');
        const containerRect = container.getBoundingClientRect();
        const elementRect = element.getBoundingClientRect();

        if (elementRect.top < containerRect.top || elementRect.bottom > containerRect.bottom) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    showLoading() {
        const content = this.lyricsOverlay.querySelector('.lyrics-content');
        content.innerHTML = '<div class="lyrics-loading">🎤 Searching for lyrics...</div>';
        this.lyricsOverlay.style.display = 'block';
    }

    showError(message) {
        const content = this.lyricsOverlay.querySelector('.lyrics-content');
        content.innerHTML = `<div class="lyrics-error">❌ ${message}</div>`;
    }

    toggleLyrics() {
        this.isEnabled = !this.isEnabled;
        const toggleBtn = document.getElementById('lyrics-toggle');

        if (this.isEnabled) {
            toggleBtn.textContent = '⏸️';
            this.startSynchronization();
        } else {
            toggleBtn.textContent = '▶️';
            this.stopSynchronization();
        }
    }

    hideLyricsOverlay() {
        if (this.lyricsOverlay) {
            this.lyricsOverlay.style.display = 'none';
            this.stopSynchronization();
        }
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'toggleLyrics') {
                this.toggleLyrics();
            } else if (request.action === 'getCurrentSong') {
                sendResponse(this.currentSong);
            }
        });
    }
}

// Initialize extension when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new YouTubeMusicLyricsExtension();
    });
} else {
    new YouTubeMusicLyricsExtension();
}
