/* Styles for YouTube Music Live Lyrics Extension */

#lyrics-overlay {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 350px;
    max-height: 500px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid #ff6b35;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(255, 107, 53, 0.3);
    z-index: 10000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    backdrop-filter: blur(10px);
    display: none;
    overflow: hidden;
}

.lyrics-header {
    background: linear-gradient(90deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
    user-select: none;
}

.lyrics-title {
    font-weight: bold;
    font-size: 14px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.lyrics-controls {
    display: flex;
    gap: 8px;
}

.lyrics-controls button {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.lyrics-controls button:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.lyrics-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 16px;
    color: #ffffff;
    line-height: 1.6;
}

.lyrics-content::-webkit-scrollbar {
    width: 6px;
}

.lyrics-content::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 3px;
}

.lyrics-content::-webkit-scrollbar-thumb {
    background: #ff6b35;
    border-radius: 3px;
}

.lyrics-content::-webkit-scrollbar-thumb:hover {
    background: #f7931e;
}

.lyrics-line {
    padding: 4px 0;
    margin: 2px 0;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 14px;
    opacity: 0.6;
}

.lyrics-line.active {
    background: linear-gradient(90deg, rgba(255, 107, 53, 0.3) 0%, rgba(247, 147, 30, 0.3) 100%);
    color: #ff6b35;
    font-weight: bold;
    opacity: 1;
    transform: scale(1.02);
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.2);
}

.lyrics-line.passed {
    opacity: 0.4;
    color: #cccccc;
}

.static-lyrics {
    white-space: pre-line;
    font-size: 14px;
    line-height: 1.8;
    color: #ffffff;
}

.lyrics-loading {
    text-align: center;
    color: #ff6b35;
    font-style: italic;
    padding: 20px;
    animation: pulse 2s infinite;
}

.lyrics-error {
    text-align: center;
    color: #ff4444;
    padding: 20px;
    font-style: italic;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* Smooth animations */
.lyrics-line {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 0.6; transform: translateY(0); }
}

/* Responsive design */
@media (max-width: 768px) {
    #lyrics-overlay {
        width: 300px;
        top: 10px;
        right: 10px;
    }

    .lyrics-content {
        max-height: 350px;
        padding: 12px;
    }

    .lyrics-line {
        font-size: 13px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    #lyrics-overlay {
        border-width: 3px;
        background: #000000;
    }

    .lyrics-line.active {
        background: #ffffff;
        color: #000000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .lyrics-line,
    .lyrics-controls button,
    #lyrics-overlay {
        transition: none;
        animation: none;
    }
}

/* Dark theme adjustments for YouTube Music */
[dark] #lyrics-overlay {
    border-color: #ff6b35;
    box-shadow: 0 8px 32px rgba(255, 107, 53, 0.4);
}

/* Custom scrollbar for better integration */
.lyrics-content {
    scrollbar-width: thin;
    scrollbar-color: #ff6b35 rgba(255,255,255,0.1);
}
