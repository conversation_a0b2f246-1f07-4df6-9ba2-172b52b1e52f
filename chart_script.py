import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

# Define the data
components = [
    {"name": "manifest.json", "type": "config", "description": "Config & permissions"},
    {"name": "content.js", "type": "content_script", "description": "YT Music DOM integration"},
    {"name": "background.js", "type": "service_worker", "description": "API calls & lyrics fetch"},
    {"name": "popup.html/js", "type": "user_interface", "description": "Settings & controls"},
    {"name": "styles.css", "type": "styling", "description": "Lyrics overlay style"},
    {"name": "lyrics-sync.js", "type": "sync_engine", "description": "Real-time sync"},
    {"name": "YouTube Music", "type": "external_service", "description": "music.youtube.com"},
    {"name": "Lyrics APIs", "type": "external_service", "description": "<PERSON>ius, <PERSON>sixmatch"},
    {"name": "Browser Storage", "type": "storage", "description": "Settings & cache"}
]

data_flow = [
    {"from": "YouTube Music", "to": "content.js", "description": "Song detection"},
    {"from": "content.js", "to": "background.js", "description": "Fetch request"},
    {"from": "background.js", "to": "Lyrics APIs", "description": "API calls"},
    {"from": "Lyrics APIs", "to": "background.js", "description": "Lyrics data"},
    {"from": "background.js", "to": "content.js", "description": "Processed lyrics"},
    {"from": "content.js", "to": "lyrics-sync.js", "description": "Sync timing"},
    {"from": "lyrics-sync.js", "to": "content.js", "description": "Display updates"},
    {"from": "popup.html/js", "to": "content.js", "description": "User settings"},
    {"from": "Browser Storage", "to": "background.js", "description": "Cached data"}
]

# Define colors for different component types
color_map = {
    "config": "#1FB8CD",
    "content_script": "#FFC185", 
    "service_worker": "#ECEBD5",
    "user_interface": "#5D878F",
    "styling": "#D2BA4C",
    "sync_engine": "#B4413C",
    "external_service": "#964325",
    "storage": "#944454"
}

# Define positions for components in a logical architecture layout
positions = {
    "manifest.json": (2, 9),
    "popup.html/js": (1, 7),
    "styles.css": (3, 7),
    "content.js": (5, 6),
    "lyrics-sync.js": (7, 6),
    "background.js": (5, 4),
    "YouTube Music": (2, 2),
    "Lyrics APIs": (8, 4),
    "Browser Storage": (2, 4)
}

# Create the figure
fig = go.Figure()

# Add components as rectangles
for comp in components:
    x, y = positions[comp["name"]]
    
    # Add the component box
    fig.add_trace(go.Scatter(
        x=[x], y=[y],
        mode='markers+text',
        marker=dict(
            size=80,
            color=color_map[comp["type"]],
            symbol='square',
            line=dict(width=2, color='black')
        ),
        text=comp["name"],
        textposition="middle center",
        textfont=dict(size=9, color='black', family="Arial Black"),
        name=comp["type"].replace("_", " ").title(),
        hovertemplate=f"<b>{comp['name']}</b><br>{comp['description']}<extra></extra>",
        showlegend=True,
        cliponaxis=False
    ))

# Add data flow arrows using shapes instead of annotations
shapes = []
for flow in data_flow:
    from_pos = positions[flow["from"]]
    to_pos = positions[flow["to"]]
    
    # Calculate arrow direction
    dx = to_pos[0] - from_pos[0]
    dy = to_pos[1] - from_pos[1]
    
    # Add arrow line as shape
    shapes.append(dict(
        type="line",
        x0=from_pos[0], y0=from_pos[1],
        x1=to_pos[0], y1=to_pos[1],
        line=dict(color="gray", width=2),
        layer="below"
    ))

# Update layout with shapes
fig.update_layout(
    title="YouTube Music Live Lyrics Extension",
    xaxis=dict(
        showgrid=False, 
        showticklabels=False, 
        zeroline=False,
        range=[0, 10]
    ),
    yaxis=dict(
        showgrid=False, 
        showticklabels=False, 
        zeroline=False,
        range=[0, 10]
    ),
    plot_bgcolor='white',
    shapes=shapes,
    legend=dict(
        orientation='h',
        yanchor='bottom',
        y=1.05,
        xanchor='center',
        x=0.5
    )
)

# Remove duplicate legend entries
seen_types = set()
for trace in fig.data:
    if trace.name in seen_types:
        trace.showlegend = False
    else:
        seen_types.add(trace.name)

fig.write_image("youtube_music_extension_architecture.png")