# YouTube Music Live Lyrics Extension

A browser extension for Chrome and Microsoft Edge that brings live, auto-scrolling synchronized lyrics to YouTube Music. Transform your music listening experience with real-time lyrics that scroll perfectly in sync with your favorite songs.

## 🌟 Features

- **Live Synchronized Lyrics**: Real-time lyrics that scroll automatically with the music
- **Auto-Scrolling**: Smooth, synchronized scrolling that follows the song's progress
- **Floating Overlay**: Draggable, customizable lyrics overlay that doesn't interfere with YouTube Music
- **Multiple Lyrics Sources**: Fetches lyrics from various APIs and sources for maximum coverage
- **LRC Format Support**: Supports industry-standard LRC (synchronized lyrics) files
- **Cross-Browser Compatibility**: Works on both Chrome and Microsoft Edge
- **Dark Theme Integration**: Seamlessly integrates with YouTube Music's interface
- **Offline Caching**: Saves lyrics locally for faster loading

## 🚀 Installation

### Development Installation

1. **Download the Extension**
   ```bash
   git clone https://github.com/your-username/youtube-music-lyrics-extension.git
   cd youtube-music-lyrics-extension
   ```

2. **Load in Chrome/Edge**
   - Open Chrome/Edge and navigate to `chrome://extensions/` or `edge://extensions/`
   - Enable "Developer mode" (toggle in top right)
   - Click "Load unpacked"
   - Select the extension folder

3. **Grant Permissions**
   - The extension will request permissions for:
     - Access to music.youtube.com
     - Access to lyrics APIs
     - Local storage for settings

### Production Installation (Future)
- Will be available on Chrome Web Store and Edge Add-ons store

## 🎵 How to Use

1. **Open YouTube Music**: Navigate to [music.youtube.com](https://music.youtube.com)
2. **Play a Song**: Start playing any song
3. **Lyrics Appear**: The extension automatically detects the song and fetches lyrics
4. **Auto-Scroll**: Lyrics scroll in sync with the music playback
5. **Customize**: Use the popup to toggle features and adjust settings

### Extension Popup Controls

- **Enable Lyrics**: Toggle lyrics fetching on/off
- **Auto-scroll**: Enable/disable automatic scrolling
- **Show Overlay**: Toggle the floating lyrics overlay
- **Refresh Lyrics**: Manually refresh lyrics for the current song

## 🛠️ Technical Architecture

### Core Components

```
youtube-music-lyrics-extension/
├── manifest.json          # Extension configuration
├── content.js             # Main content script (YouTube Music integration)
├── background.js          # Service worker (API calls, lyrics fetching)
├── popup.html             # Popup interface
├── popup.js              # Popup logic
├── styles.css            # Lyrics overlay styling
└── lyrics-sync.js        # Advanced synchronization engine
```

### Key Technologies

- **Manifest V3**: Latest Chrome extension standard
- **Content Scripts**: Direct integration with YouTube Music DOM
- **Service Workers**: Background processing for API calls
- **HTML5 Audio API**: Precise playback time detection
- **LRC Parser**: Industry-standard lyrics format support
- **CSS Animations**: Smooth scrolling and transitions

## 📡 Lyrics Sources

The extension attempts to fetch lyrics from multiple sources:

1. **Primary APIs**:
   - Lyrics.ovh (free, public API)
   - Genius API (requires API key)
   - Musixmatch API (premium features)

2. **Fallback Methods**:
   - Web scraping (limited, respects ToS)
   - User-uploaded LRC files
   - Community databases

### Legal Considerations

- **Respects Terms of Service**: Complies with website ToS
- **Fair Use**: Lyrics display for personal, non-commercial use
- **Attribution**: Credits lyrics sources when displayed
- **No Redistribution**: Doesn't store or redistribute copyrighted content

## ⚙️ Configuration

### Extension Settings

Accessible via the popup interface:

- **Lyrics Source Priority**: Choose preferred lyrics providers
- **Sync Offset**: Adjust timing offset (±500ms)
- **Display Options**: Customize overlay appearance
- **Auto-hide**: Auto-hide overlay when not playing

### Advanced Configuration

For developers, additional settings in `background.js`:

```javascript
const CONFIG = {
  SYNC_INTERVAL: 100,      // Sync update frequency (ms)
  LYRICS_CACHE_SIZE: 50,   // Number of lyrics to cache
  API_TIMEOUT: 5000,       // API request timeout (ms)
  SCROLL_BEHAVIOR: 'smooth' // Scrolling animation style
};
```

## 🔧 Development

### Prerequisites

- Chrome/Edge browser
- Basic knowledge of JavaScript, HTML, CSS
- Understanding of browser extension development

### Local Development

1. **Make Changes**: Edit any of the extension files
2. **Reload Extension**: Go to `chrome://extensions/` and click reload
3. **Test**: Refresh YouTube Music and test functionality
4. **Debug**: Use browser DevTools for debugging

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 🐛 Troubleshooting

### Common Issues

**Lyrics Not Appearing**
- Check if you're on music.youtube.com
- Verify the extension is enabled
- Try refreshing the page
- Check browser console for errors

**Sync Issues**
- Adjust sync offset in settings
- Ensure stable internet connection
- Try refreshing lyrics

**Performance Issues**
- Disable other extensions temporarily
- Clear browser cache
- Check for browser updates

### Debug Mode

Enable debug logging by setting:
```javascript
localStorage.setItem('lyricsDebug', 'true');
```

## 📊 Browser Compatibility

| Browser | Version | Status |
|---------|---------|--------|
| Chrome | 88+ | ✅ Fully Supported |
| Edge | 88+ | ✅ Fully Supported |
| Firefox | N/A | ❌ Not Compatible (Manifest V3) |
| Safari | N/A | ❌ Not Compatible |

## 🔒 Privacy & Security

- **No Data Collection**: Extension doesn't collect personal data
- **Local Storage Only**: Settings stored locally on your device
- **Secure APIs**: Uses HTTPS for all external requests
- **Minimal Permissions**: Requests only necessary permissions
- **Open Source**: Code is publicly auditable

## 📄 Legal & Licensing

### Extension License
- **MIT License**: Free to use, modify, and distribute

### Lyrics Content
- **Fair Use**: Lyrics displayed for personal use only
- **No Storage**: Lyrics are not permanently stored
- **Attribution**: Credits original sources
- **Compliance**: Respects copyright and ToS

### Disclaimer
This extension is for educational and personal use only. Users are responsible for complying with applicable laws and terms of service.

## 🤝 Acknowledgments

- **YouTube Music**: For providing an amazing music platform
- **Lyrics Providers**: Genius, Musixmatch, and other lyrics services
- **Open Source Community**: For tools and libraries used
- **Beta Testers**: For feedback and bug reports

## 📮 Support & Contact

- **Issues**: [GitHub Issues](https://github.com/your-username/youtube-music-lyrics-extension/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/youtube-music-lyrics-extension/discussions)
- **Email**: <EMAIL>

## 🗺️ Roadmap

### Version 1.1
- [ ] Multiple language support
- [ ] Custom themes
- [ ] Karaoke mode
- [ ] Lyrics timing adjustment

### Version 1.2
- [ ] Offline lyrics database
- [ ] Social sharing features
- [ ] Playlist lyrics export
- [ ] Mobile support

### Version 2.0
- [ ] AI-powered lyrics generation
- [ ] Voice recognition sync
- [ ] Collaborative lyrics editing
- [ ] Advanced analytics

---

Made with ❤️ for music lovers everywhere