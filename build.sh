#!/bin/bash
# Build script for YouTube Music Live Lyrics Extension

echo "🔨 Building YouTube Music Live Lyrics Extension..."

# Create build directory
mkdir -p build

# Copy extension files
cp manifest.json build/
cp content.js build/
cp background.js build/
cp popup.html build/
cp popup.js build/
cp styles.css build/
cp lyrics-sync.js build/

# Create a zip file for distribution
cd build
zip -r ../youtube-music-lyrics-extension-v1.0.0.zip .
cd ..

echo "✅ Extension packaged successfully!"
echo "📦 Package: youtube-music-lyrics-extension-v1.0.0.zip"
echo "📋 Ready for Chrome Web Store or manual installation"
